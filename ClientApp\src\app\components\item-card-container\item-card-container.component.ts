import { Component, computed } from '@angular/core';
import { ItemCardComponent } from "../item-card/item-card.component";
import { MarketplaceService } from '../../services/marketplace.service';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-item-card-container',
  standalone: true,
  imports: [CommonModule, ItemCardComponent],
  templateUrl: './item-card-container.component.html',
  styleUrl: './item-card-container.component.scss'
})
export class ItemCardContainerComponent {
  constructor(private marketplaceService: MarketplaceService){}

  cartItems = computed(() => this.marketplaceService.allItems());

  clearAllClicked(){
    this.marketplaceService.clear();
  }
}
