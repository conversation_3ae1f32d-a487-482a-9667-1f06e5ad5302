import { Component, Input, input, signal } from '@angular/core';
import { Item } from '../../models/item';
import { MarketplaceService } from '../../services/marketplace.service';

@Component({
  selector: 'app-item-card',
  standalone: true,
  imports: [],
  templateUrl: './item-card.component.html',
  styleUrl: './item-card.component.scss'
})
export class ItemCardComponent {

  constructor(private marketplaceService: MarketplaceService){}

  @Input({required: true}) set itemInput(value: Item){
    this.item.set(value);
  }

    item = signal<Item>(null!);


  // item = signal<Item>({
  //   id: 1,
  //   name: 'TestItem',
  //   price: 99.99,
  //   quantity: 1
  // });

  addButtonClicked() {
    this.marketplaceService.addItem(this.item());
  }
  
}
