<div>
    <app-item-card 
    *ngFor="let item of cartItems()"
    [itemInput]="item"/>

    <!-- <app-item-card [itemInput]="cartItems()"/>
    <app-item-card/>
    <app-item-card/>
    <app-item-card/>
    <app-item-card/>
    <app-item-card/>
    <app-item-card/>
    <app-item-card/>
    <app-item-card/>
    <app-item-card/>
    <app-item-card/>
    <app-item-card/>
    <app-item-card/>
    <app-item-card/>
    <app-item-card/>
    <app-item-card/>
    <app-item-card/>
    <app-item-card/>
    <app-item-card/>
    <app-item-card/> -->
</div>

<button (click)="clearAllClicked()">CLEAR ALL</button>