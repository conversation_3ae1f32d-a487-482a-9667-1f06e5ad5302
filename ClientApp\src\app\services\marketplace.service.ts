import { Injectable, signal } from '@angular/core';
import { Item } from '../models/item';
import { ItemService } from './api/item.service';

@Injectable({
  providedIn: 'root',
})
export class MarketplaceService {
  constructor(private itemService: ItemService) {}

  allItems = signal<Item[]>([
    {
      id: 1,
      name: 'Alpha',
      price: 19.99,
      quantity: 1,
    },
    {
      id: 2,
      name: 'Bravo',
      price: 29.99,
      quantity: 1,
    },
    {
      id: 3,
      name: 'Charlie',
      price: 39.99,
      quantity: 1,
    },
  ]);

  cartItems = signal<Item[]>([]);

  addItem(item: Item) {
    this.cartItems.update((items) => [...items, item]);
    this.saveItemIdsToStorage();
  }

  removeItem(id: number) {
    this.cartItems.update((items) => items.filter((i) => i.id !== id));
    this.saveItemIdsToStorage();
  }

  clear() {
    this.cartItems.set([]);
    this.saveItemIdsToStorage();
    // var blah = this.itemService.getAll();
    // console.log(blah);
  }

  private saveItemIdsToStorage() {
    const ids = this.cartItems().map((item) => item.id);
    localStorage.setItem('cartItemIds', JSON.stringify(ids));
  }
}
